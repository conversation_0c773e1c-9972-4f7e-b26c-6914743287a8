import { App } from "@/core.constants";
import { AppFilters } from "@/types/filters";

export const filterApps = (apps: App[], filters: AppFilters): App[] => {
  return apps.filter((app) => {
    if (filters.category !== "all" && app.type !== filters.category) {
      return false;
    }

    const { booleanFilters } = filters;

    if (booleanFilters.has2FA !== null) {
      const hasYubikey = app.yubikeys?.enabled || false;
      if (hasYubikey !== booleanFilters.has2FA) {
        return false;
      }
    }

    if (booleanFilters.hasPinSupport !== null) {
      const hasPin = app.pin?.enabled || false;
      if (hasPin !== booleanFilters.hasPinSupport) {
        return false;
      }
    }

    if (booleanFilters.hasPhoneRequired !== null) {
      const phoneRequired = app.phoneNumber?.enabled || false;
      if (phoneRequired !== booleanFilters.hasPhoneRequired) {
        return false;
      }
    }

    if (booleanFilters.hasBackup !== null) {
      const hasBackup = app.backup?.enabled || false;
      if (hasBackup !== booleanFilters.hasBackup) {
        return false;
      }
    }

    return true;
  });
};
