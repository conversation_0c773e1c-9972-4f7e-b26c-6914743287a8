"use client";

import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import { Trash2 } from "lucide-react";

type StatusBadgeProps = {
  enabled: boolean;
  variant?: "default" | "inverse";
};

const StatusBadge = ({ enabled, variant = "default" }: StatusBadgeProps) => {
  const isPositive = variant === "default" ? enabled : !enabled;
  const colorClasses = isPositive
    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClasses}`}
    >
      {enabled ? "Yes" : "No"}
    </span>
  );
};

interface ReusableAppsTableProps {
  apps: App[];
  showActions?: boolean;
  onAppClick?: (app: App) => void;
  onEditApp?: (app: App) => void;
  onDeleteApp?: (app: App) => void;
  isDeleting?: boolean;
  className?: string;
}

export const AppsTable = ({
  apps,
  showActions = false,
  onAppClick,
  onEditApp,
  onDeleteApp,
  isDeleting = false,
  className = "",
}: ReusableAppsTableProps) => {
  const handleRowClick = (app: App) => {
    if (onAppClick && !showActions) {
      onAppClick(app);
    }
  };

  return (
    <div className={`rounded-md border ${className}`}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Logo</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Yubikey Support</TableHead>
            <TableHead>PIN Support</TableHead>
            <TableHead>Phone Required</TableHead>
            <TableHead>Backup Available</TableHead>
            {showActions && <TableHead>Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {apps.map((app) => (
            <TableRow
              key={app.id}
              className={
                !showActions && onAppClick
                  ? "cursor-pointer hover:bg-muted/50"
                  : ""
              }
              onClick={() => handleRowClick(app)}
            >
              <TableCell>
                {app.appLogo ? (
                  <Image
                    src={app.appLogo}
                    alt={`${app.name} logo`}
                    width={32}
                    height={32}
                    className="w-8 h-8 object-contain rounded"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      No Logo
                    </span>
                  </div>
                )}
              </TableCell>
              <TableCell className="font-medium">{app.name}</TableCell>
              <TableCell>{APP_TYPE_TEXT[app.type]}</TableCell>
              <TableCell>
                <StatusBadge enabled={app.yubikeys?.enabled || false} />
              </TableCell>
              <TableCell>
                <StatusBadge enabled={app.pin?.enabled || false} />
              </TableCell>
              <TableCell>
                <StatusBadge
                  enabled={app.phoneNumber?.enabled || false}
                  variant="inverse"
                />
              </TableCell>
              <TableCell>
                <StatusBadge enabled={app.backup?.enabled || false} />
              </TableCell>
              {showActions && (
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditApp?.(app);
                      }}
                      className="cursor-pointer"
                    >
                      Edit
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 cursor-pointer"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete App</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete &quot;{app.name}
                            &quot;? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteApp?.(app)}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700 cursor-pointer"
                          >
                            {isDeleting ? "Deleting..." : "Delete"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
