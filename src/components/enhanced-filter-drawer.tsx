"use client";

import React from "react";
import { Drawer } from "vaul";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { AppType, APP_TYPE_TEXT } from "@/core.constants";
import { AppFilters, BooleanFilters } from "@/types/filters";
import { Filter, X } from "lucide-react";

interface EnhancedFilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  filters: AppFilters;
  onFiltersChange: (filters: AppFilters) => void;
}

const BOOLEAN_FILTER_LABELS = {
  has2FA: "Yubikey Support",
  hasPinSupport: "PIN Support",
  hasPhoneRequired: "Phone Number Required",
  hasBackup: "Backup Available",
};

export const EnhancedFilterDrawer = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
}: EnhancedFilterDrawerProps) => {
  const handleCategoryChange = (category: string) => {
    onFiltersChange({
      ...filters,
      category: category as AppType | "all",
    });
  };

  const handleBooleanFilterChange = (
    key: keyof BooleanFilters,
    enabled: boolean
  ) => {
    // Three-state logic: null -> true -> false -> null
    const currentValue = filters.booleanFilters[key];
    let newValue: boolean | null;

    if (currentValue === null) {
      newValue = enabled;
    } else if (currentValue === enabled) {
      newValue = null; // Turn off if clicking the same state
    } else {
      newValue = enabled;
    }

    onFiltersChange({
      ...filters,
      booleanFilters: {
        ...filters.booleanFilters,
        [key]: newValue,
      },
    });
  };

  const getActiveFiltersCount = () => {
    const booleanFiltersCount = Object.values(filters.booleanFilters).filter(
      (value) => value !== null
    ).length;
    const categoryFilterCount = filters.category !== "all" ? 1 : 0;
    return booleanFiltersCount + categoryFilterCount;
  };

  const clearAllFilters = () => {
    onFiltersChange({
      category: "all",
      booleanFilters: {
        has2FA: null,
        hasPinSupport: null,
        hasPhoneRequired: null,
        hasBackup: null,
      },
    });
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Drawer.Root open={isOpen} onOpenChange={onClose}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white dark:bg-neutral-900 flex flex-col rounded-t-[10px] h-[70vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <Drawer.Title></Drawer.Title>
          <div className="p-4 bg-white dark:bg-neutral-900 rounded-t-[10px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-neutral-300 dark:bg-neutral-600 mb-6" />

            <div className="flex items-center justify-between mb-6 pb-4 border-b border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center space-x-3">
                <Filter className="w-6 h-6 text-primary" />
                <h2 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
                  Filter Apps
                </h2>
                {activeFiltersCount > 0 && (
                  <span className="px-2 py-1 bg-primary text-primary-foreground rounded-full text-xs">
                    {activeFiltersCount}
                  </span>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-6">
              <div className="space-y-3">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {Object.entries(APP_TYPE_TEXT).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <Label className="text-sm font-medium">Security Features</Label>
                {Object.entries(BOOLEAN_FILTER_LABELS).map(([key, label]) => {
                  const filterKey = key as keyof BooleanFilters;
                  const currentValue = filters.booleanFilters[filterKey];

                  return (
                    <div key={key} className="space-y-3">
                      <div className="text-sm text-neutral-700 dark:text-neutral-300">
                        {label}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant={
                            currentValue === true ? "default" : "outline"
                          }
                          size="sm"
                          className="flex-1"
                          onClick={() =>
                            handleBooleanFilterChange(filterKey, true)
                          }
                        >
                          Yes
                        </Button>
                        <Button
                          variant={
                            currentValue === false ? "default" : "outline"
                          }
                          size="sm"
                          className="flex-1"
                          onClick={() =>
                            handleBooleanFilterChange(filterKey, false)
                          }
                        >
                          No
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {activeFiltersCount > 0 && (
                <div className="pt-4 border-t border-neutral-200 dark:border-neutral-700">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={clearAllFilters}
                  >
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>

            <div className="mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700">
              <p className="text-sm text-neutral-600 dark:text-neutral-400 text-center">
                {activeFiltersCount > 0 ? (
                  <>
                    <span className="font-medium text-neutral-900 dark:text-neutral-100">
                      {activeFiltersCount}
                    </span>{" "}
                    filter{activeFiltersCount !== 1 ? "s" : ""} active
                  </>
                ) : (
                  "No filters applied"
                )}
              </p>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
