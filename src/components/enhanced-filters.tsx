"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AppType, APP_TYPE_TEXT } from "@/core.constants";
import { AppFilters, BooleanFilters } from "@/types/filters";
import { Filter, ChevronDown } from "lucide-react";

interface EnhancedFiltersProps {
  filters: AppFilters;
  onFiltersChange: (filters: AppFilters) => void;
}

const BOOLEAN_FILTER_LABELS = {
  has2FA: "Yubikey Support",
  hasPinSupport: "PIN Support",
  hasPhoneRequired: "Phone Number Required",
  hasBackup: "Backup Available",
};

export const EnhancedFilters = ({
  filters,
  onFiltersChange,
}: EnhancedFiltersProps) => {
  const handleCategoryChange = (category: string) => {
    onFiltersChange({
      ...filters,
      category: category as AppType | "all",
    });
  };

  const handleBooleanFilterChange = (
    key: keyof BooleanFilters,
    value: boolean | null
  ) => {
    onFiltersChange({
      ...filters,
      booleanFilters: {
        ...filters.booleanFilters,
        [key]: value,
      },
    });
  };

  const getActiveFiltersCount = () => {
    const booleanFiltersCount = Object.values(filters.booleanFilters).filter(
      (value) => value !== null
    ).length;
    const categoryFilterCount = filters.category !== "all" ? 1 : 0;
    return booleanFiltersCount + categoryFilterCount;
  };

  const clearAllFilters = () => {
    onFiltersChange({
      category: "all",
      booleanFilters: {
        has2FA: null,
        hasPinSupport: null,
        hasPhoneRequired: null,
        hasBackup: null,
      },
    });
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="flex items-center gap-3">
      <Select value={filters.category} onValueChange={handleCategoryChange}>
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Select category" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Categories</SelectItem>
          {Object.entries(APP_TYPE_TEXT).map(([key, value]) => (
            <SelectItem key={key} value={key}>
              {value}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="relative">
            <Filter className="w-4 h-4 mr-2" />
            Filters
            {activeFiltersCount > 0 && (
              <span className="ml-2 px-2 py-0.5 bg-primary text-primary-foreground rounded-full text-xs">
                {activeFiltersCount}
              </span>
            )}
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>Security Features</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {Object.entries(BOOLEAN_FILTER_LABELS).map(([key, label]) => {
            const filterKey = key as keyof BooleanFilters;
            const currentValue = filters.booleanFilters[filterKey];

            return (
              <div key={key} className="px-2 py-1">
                <div className="text-sm font-medium mb-1">{label}</div>
                <div className="flex gap-1">
                  <Button
                    variant={currentValue === true ? "default" : "outline"}
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() =>
                      handleBooleanFilterChange(
                        filterKey,
                        currentValue === true ? null : true
                      )
                    }
                  >
                    Yes
                  </Button>
                  <Button
                    variant={currentValue === false ? "default" : "outline"}
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() =>
                      handleBooleanFilterChange(
                        filterKey,
                        currentValue === false ? null : false
                      )
                    }
                  >
                    No
                  </Button>
                </div>
              </div>
            );
          })}

          {activeFiltersCount > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="px-2 py-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full h-7"
                  onClick={clearAllFilters}
                >
                  Clear All
                </Button>
              </div>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
