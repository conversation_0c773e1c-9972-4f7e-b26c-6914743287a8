"use client";

import React from "react";
import { Drawer } from "vaul";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AppType } from "@/core.constants";
import { Filter, X } from "lucide-react";

interface FilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFilter: AppType | "all";
  onFilterChange: (filter: AppType | "all") => void;
  filterButtons: Array<{ key: string; label: string }>;
}

export const FilterDrawer = ({
  isOpen,
  onClose,
  selectedFilter,
  onFilterChange,
  filterButtons,
}: FilterDrawerProps) => {
  const handleFilterSelect = (filter: AppType | "all") => {
    onFilterChange(filter);
    onClose();
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={onClose}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white dark:bg-neutral-900 flex flex-col rounded-t-[10px] h-[60vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <Drawer.Title></Drawer.Title>
          <div className="p-4 bg-white dark:bg-neutral-900 rounded-t-[10px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-neutral-300 dark:bg-neutral-600 mb-6" />

            <div className="flex items-center justify-between mb-6 pb-4 border-b border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center space-x-3">
                <Filter className="w-6 h-6 text-primary" />
                <h2 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
                  Filter Apps
                </h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-3">
              {filterButtons.map((filter) => (
                <Button
                  key={filter.key}
                  variant={
                    selectedFilter === filter.key ? "default" : "outline"
                  }
                  size="lg"
                  onClick={() =>
                    handleFilterSelect(filter.key as AppType | "all")
                  }
                  className="w-full justify-start text-left h-12 text-base"
                >
                  {filter.label}
                  {selectedFilter === filter.key && (
                    <span className="ml-auto text-xs opacity-70">✓</span>
                  )}
                </Button>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700">
              <p className="text-sm text-neutral-600 dark:text-neutral-400 text-center">
                Currently showing:{" "}
                <span className="font-medium text-neutral-900 dark:text-neutral-100">
                  {filterButtons.find((f) => f.key === selectedFilter)?.label}
                </span>
              </p>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
