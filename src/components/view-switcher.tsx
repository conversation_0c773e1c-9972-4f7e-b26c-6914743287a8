"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Grid3X3, List } from "lucide-react";
import { cn } from "@/lib/utils";

export type ViewMode = "cards" | "table";

interface ViewSwitcherProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
}

export const ViewSwitcher = ({
  currentView,
  onViewChange,
  className,
}: ViewSwitcherProps) => {
  return (
    <div className={cn("flex items-center rounded-lg border border-border p-1", className)}>
      <Button
        variant={currentView === "cards" ? "default" : "ghost"}
        size="sm"
        onClick={() => onViewChange("cards")}
        className={cn(
          "h-8 px-3 rounded-md transition-all",
          currentView === "cards"
            ? "bg-primary text-primary-foreground shadow-sm"
            : "text-muted-foreground hover:text-foreground hover:bg-muted"
        )}
      >
        <Grid3X3 className="w-4 h-4 mr-2" />
        Cards
      </Button>
      <Button
        variant={currentView === "table" ? "default" : "ghost"}
        size="sm"
        onClick={() => onViewChange("table")}
        className={cn(
          "h-8 px-3 rounded-md transition-all",
          currentView === "table"
            ? "bg-primary text-primary-foreground shadow-sm"
            : "text-muted-foreground hover:text-foreground hover:bg-muted"
        )}
      >
        <List className="w-4 h-4 mr-2" />
        Table
      </Button>
    </div>
  );
};
