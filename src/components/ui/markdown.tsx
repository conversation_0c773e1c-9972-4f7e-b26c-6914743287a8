"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import { cn } from "@/lib/utils";

interface MarkdownProps {
  content: string;
  className?: string;
}

export const Markdown = ({ content, className }: MarkdownProps) => {
  return (
    <div
      className={cn(
        // Headers - match SafeCheck app styling
        "[&_h1]:text-foreground [&_h1]:font-semibold [&_h1]:tracking-tight",
        "[&_h1]:text-2xl [&_h1]:mb-6 [&_h1]:mt-8 [&_h1]:border-b [&_h1]:border-border [&_h1]:pb-3",
        "[&_h2]:text-xl [&_h2]:mb-4 [&_h2]:mt-6 [&_h2]:border-b [&_h2]:border-border/50 [&_h2]:pb-2",
        "[&_h3]:text-lg [&_h3]:mb-3 [&_h3]:mt-5",
        "[&_h4]:text-base [&_h4]:mb-2 [&_h4]:mt-4 [&_h4]:font-medium",
        "[&_h5]:text-sm [&_h5]:mb-2 [&_h5]:mt-3 [&_h5]:font-medium",
        "[&_h6]:text-sm [&_h6]:mb-2 [&_h6]:mt-3 [&_h6]:font-medium [&_h6]:text-muted-foreground",

        // Text elements - match SafeCheck muted colors
        "[&_p]:text-muted-foreground [&_p]:leading-relaxed [&_p]:mb-4",
        "[&_strong]:text-foreground [&_strong]:font-semibold",
        "[&_em]:text-foreground [&_em]:italic",

        // Code styling - match SafeCheck card/input styling
        "[&_code]:text-foreground [&_code]:bg-muted [&_code]:border [&_code]:border-border",
        "[&_code]:px-1.5 [&_code]:py-0.5 [&_code]:rounded-md [&_code]:text-sm [&_code]:font-mono",
        "[&_code]:before:content-none [&_code]:after:content-none",
        "[&_pre]:bg-card [&_pre]:border [&_pre]:border-border [&_pre]:rounded-lg",
        "[&_pre]:p-4 [&_pre]:overflow-x-auto [&_pre]:text-sm",
        "[&_pre]:shadow-sm dark:[&_pre]:bg-muted/50",

        // Lists - match SafeCheck spacing and colors
        "[&_ul]:text-muted-foreground [&_ol]:text-muted-foreground",
        "[&_li]:text-muted-foreground [&_li]:mb-1",
        "[&_ul]:space-y-1 [&_ol]:space-y-1",
        "[&_ul]:pl-6 [&_ol]:pl-6",

        // Links - match SafeCheck primary colors
        "[&_a]:text-primary [&_a]:no-underline [&_a]:font-medium",
        "[&_a:hover]:underline [&_a:hover]:text-primary/80",
        "[&_a]:transition-colors [&_a]:duration-200",

        // Blockquotes - match SafeCheck card styling
        "[&_blockquote]:border-l-4 [&_blockquote]:border-l-border [&_blockquote]:bg-muted/30",
        "[&_blockquote]:text-muted-foreground [&_blockquote]:pl-4 [&_blockquote]:py-2",
        "[&_blockquote]:italic [&_blockquote]:rounded-r-md [&_blockquote]:my-4",

        // Tables - match SafeCheck card and border styling
        "[&_table]:border-collapse [&_table]:border [&_table]:border-border [&_table]:rounded-lg [&_table]:overflow-hidden",
        "[&_thead]:bg-muted [&_thead]:text-foreground",
        "[&_th]:border [&_th]:border-border [&_th]:px-4 [&_th]:py-2 [&_th]:text-left [&_th]:font-semibold [&_th]:text-sm",
        "[&_td]:border [&_td]:border-border [&_td]:px-4 [&_td]:py-2 [&_td]:text-sm [&_td]:text-muted-foreground",
        "[&_tbody_tr:nth-child(even)]:bg-muted/30",

        // Horizontal rules
        "[&_hr]:border-border [&_hr]:my-6",

        // Images
        "[&_img]:rounded-lg [&_img]:border [&_img]:border-border [&_img]:shadow-sm",

        className
      )}
    >
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
};
