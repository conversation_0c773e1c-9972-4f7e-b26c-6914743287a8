"use client";

import React from "react";
import Link from "next/link";

export const DeveloperCredit = () => {
  return (
    <div className="fixed bottom-4 right-4 z-50 safe-area-inset-bottom">
      <Link
        href="https://x.com/0xm<PERSON><PERSON>"
        rel="noopener noreferrer"
        target="_blank"
        className="group flex items-center gap-2 px-2 py-2 sm:px-4 rounded-full bg-gradient-to-r opacity-70 hover:opacity-100 from-blue-600 via-purple-600 to-blue-800 hover:from-blue-700 hover:via-purple-700 hover:to-blue-900 text-white text-xs sm:text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 backdrop-blur-sm border border-white/10"
      >
        <span className="sm:hidden text-lg font-bold size-4 flex items-center justify-center">
          𝕏
        </span>

        <div className="hidden sm:flex items-center gap-2">
          <span className="text-xs opacity-90">done by</span>
          <span className="font-semibold">@0xmrudenko</span>
        </div>
      </Link>
    </div>
  );
};
