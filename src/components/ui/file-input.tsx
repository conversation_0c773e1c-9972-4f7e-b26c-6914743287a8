"use client";

import React, { useRef } from "react";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FileInputProps {
  onFileSelect: (file: File) => void;
  accept?: string;
  disabled?: boolean;
  loading?: boolean;
  currentFile?: string;
  onRemoveFile?: () => void;
  placeholder?: string;
  className?: string;
}

export const FileInput = ({
  onFileSelect,
  accept = "image/*",
  disabled = false,
  loading = false,
  currentFile,
  onRemoveFile,
  placeholder = "Choose file",
  className,
}: FileInputProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileSelect(file);
      e.target.value = "";
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-2", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        disabled={disabled || loading}
        className="hidden"
      />
      <Button
        type="button"
        onClick={handleClick}
        disabled={disabled || loading}
        variant="outline"
        className="cursor-pointer"
      >
        {loading ? (
          <>
            <div className="w-4 h-4 mr-2 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            Uploading...
          </>
        ) : currentFile ? (
          <>
            <ImageIcon className="w-4 h-4 mr-2 text-green-600" />
            File Selected
          </>
        ) : (
          <>
            <Upload className="w-4 h-4 mr-2" />
            {placeholder}
          </>
        )}
      </Button>
      {currentFile && onRemoveFile && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            onRemoveFile();
          }}
          className="text-red-600 hover:text-red-700 ml-2"
        >
          <X className="w-4 h-4 mr-1" />
          Remove
        </Button>
      )}
    </div>
  );
};
