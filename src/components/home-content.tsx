"use client";

import { getApps } from "@/api/app-api";
import { AppDetailsDrawer } from "@/app/home-components/app-details-drawer";
import { AppDetailsModal } from "@/app/home-components/app-details-modal";
import { EnhancedFilterDrawer } from "@/components/enhanced-filter-drawer";
import { EnhancedFilters } from "@/components/enhanced-filters";
import { TextEffect } from "@/components/motion-primitives/text-effect";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import { Button } from "@/components/ui/button";
import { ViewMode, ViewSwitcher } from "@/components/view-switcher";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import { AppFilters, DEFAULT_APP_FILTERS } from "@/types/filters";
import { filterApps } from "@/utils/filter-apps";
import { Filter, Shield } from "lucide-react";
import { useEffect, useState } from "react";
import { AppsTable } from "./apps-table";

export const HomeContent = () => {
  const [apps, setApps] = useState<App[]>([]);
  const [filteredApps, setFilteredApps] = useState<App[]>([]);
  const [filters, setFilters] = useState<AppFilters>(DEFAULT_APP_FILTERS);
  const [currentView, setCurrentView] = useState<ViewMode>("cards");
  const [loading, setLoading] = useState(true);
  const [selectedApp, setSelectedApp] = useState<App | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const fetchApps = async () => {
      try {
        setLoading(true);
        const result = await getApps(50);
        setApps(result.apps);
        setFilteredApps(result.apps);
      } catch (error) {
        console.error("Error fetching apps:", error);
        setApps([]);
        setFilteredApps([]);
      } finally {
        setLoading(false);
      }
    };

    fetchApps();
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    const filtered = filterApps(apps, filters);
    setFilteredApps(filtered);
  }, [filters, apps]);

  const handleAppClick = (app: App) => {
    setSelectedApp(app);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedApp(null);
  };

  return (
    <main className="min-h-screen bg-[#1f2728]/80">
      <section className="relative pt-24 pb-16">
        <div className="absolute inset-0 -z-10 size-full [background:radial-gradient(125%_125%_at_50%_100%,transparent_0%,var(--color-background)_75%)]"></div>
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center">
            <h1 className="mt-8 max-w-4xl mx-auto text-balance text-5xl font-medium md:text-6xl lg:mt-16">
              <TextEffect preset="fade-in-blur" speedSegment={0.3} as="span">
                SafeCheck
              </TextEffect>{" "}
            </h1>
            <TextEffect
              per="line"
              preset="fade-in-blur"
              speedSegment={0.3}
              delay={0.5}
              as="p"
              className="mt-8 max-w-3xl mx-auto text-pretty text-lg text-muted-foreground"
            >
              Check where you can use YubiKeys, remove your phone number, set up
              a backup plan, or revoke sessions in case of an emergency
            </TextEffect>
          </div>
        </div>
      </section>

      <section className="pb-8 hidden md:block">
        <div className="mx-auto max-w-7xl px-6">
          <div className="flex items-center justify-center gap-6">
            <ViewSwitcher
              currentView={currentView}
              onViewChange={setCurrentView}
            />
            <EnhancedFilters filters={filters} onFiltersChange={setFilters} />
          </div>
        </div>
      </section>

      <section className="pb-24 md:pb-16">
        <div className="mx-auto max-w-7xl px-6">
          {loading ? (
            <div className="flex justify-center items-center py-16">
              <div className="text-muted-foreground">Loading apps...</div>
            </div>
          ) : filteredApps.length > 0 ? (
            currentView === "cards" ? (
              <BentoGrid className="mx-auto">
                {filteredApps.map((app) => {
                  const getCardSize = () => {
                    return "col-span-1 row-span-1";
                  };

                  return (
                    <BentoCard
                      key={app.id}
                      name={app.name}
                      className={`cursor-pointer ${getCardSize()}`}
                      logo={app.appLogo}
                      Icon={Shield}
                      href="#"
                      cta="View Details"
                      type={APP_TYPE_TEXT[app.type]}
                      appType={app.type}
                      has2FA={app.yubikeys.enabled}
                      hasBackup={app.backup.enabled}
                      hasPhoneRequired={app.phoneNumber.enabled}
                      hasEmergencyAccess={app.emergency.links.length > 0}
                      hasPinSupport={app?.pin?.enabled}
                      onClick={() => handleAppClick(app)}
                    />
                  );
                })}
              </BentoGrid>
            ) : (
              <AppsTable
                apps={filteredApps}
                showActions={false}
                onAppClick={handleAppClick}
              />
            )
          ) : (
            <div className="text-center py-16">
              <div className="text-muted-foreground">
                No apps found matching your filters.
              </div>
            </div>
          )}
        </div>
      </section>

      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 z-40 p-4">
          <div className="flex justify-center gap-3">
            <ViewSwitcher
              currentView={currentView}
              onViewChange={setCurrentView}
              className="bg-background/95 backdrop-blur-sm"
            />
            <Button
              onClick={() => setIsFilterDrawerOpen(true)}
              className="h-10 px-6 text-sm font-medium shadow-lg shadow-black/10 dark:shadow-black/20 relative"
              variant="default"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {(filters.category !== "all" ||
                Object.values(filters.booleanFilters).some(
                  (v) => v !== null
                )) && (
                <span className="ml-2 px-2 py-0.5 bg-background/20 rounded-full text-xs">
                  {Object.values(filters.booleanFilters).filter(
                    (v) => v !== null
                  ).length + (filters.category !== "all" ? 1 : 0)}
                </span>
              )}
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        <AppDetailsDrawer
          onClose={handleCloseModal}
          app={selectedApp}
          isOpen={isModalOpen}
        />
      ) : (
        <AppDetailsModal
          app={selectedApp}
          onClose={handleCloseModal}
          isOpen={isModalOpen}
        />
      )}

      {isMobile && (
        <EnhancedFilterDrawer
          isOpen={isFilterDrawerOpen}
          filters={filters}
          onClose={() => setIsFilterDrawerOpen(false)}
          onFiltersChange={setFilters}
        />
      )}
    </main>
  );
};
