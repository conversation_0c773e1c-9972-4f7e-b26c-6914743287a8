import { useCallback, useEffect, useState } from "react";

import { useIntersectionObserver } from "./use-intersection-observer";
import { Spinner } from "@/shared/spinner";

type PaginationRequestDTO = {
  initialRequest: () => Promise<void>;
  fetchRequest: () => Promise<void>;
  hasMoreItems: boolean;
};

export const usePaginationRequest = ({
  initialRequest,
  fetchRequest,
  hasMoreItems,
}: PaginationRequestDTO) => {
  const [loadingMore, setLoadingMore] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold: 0.5,
  });

  const loadMoreItems = useCallback(async () => {
    if (hasMoreItems && !loadingMore) {
      setLoadingMore(true);
      await fetchRequest();
      setLoadingMore(false);
    }
  }, [fetchRequest, hasMoreItems, loadingMore]);

  useEffect(() => {
    if (isIntersecting && !initialLoading) {
      loadMoreItems();
    }
  }, [isIntersecting, initialLoading, loadMoreItems]);

  useEffect(() => {
    if (initialLoading && !loadingMore) {
      setLoadingMore(true);
      initialRequest().then(() => {
        setInitialLoading(false);
        setLoadingMore(false);
      });
    }
  }, [initialLoading, loadingMore, initialRequest]);

  const loadingMarkup = hasMoreItems ? (
    <div ref={ref} className="flex h-[30px] items-center justify-center p-4">
      {(initialLoading || loadingMore) && <Spinner />}
    </div>
  ) : initialLoading ? (
    <Spinner className="my-2 flex justify-center" size={24} />
  ) : null;

  const reset = useCallback(() => {
    setInitialLoading(true);
  }, []);

  return {
    loadingMarkup,
    setInitialLoading,
    initialLoading,
    loadingMore,
    hasMoreItems,
    ref,
    loadMoreItems,
    reset,
  };
};
