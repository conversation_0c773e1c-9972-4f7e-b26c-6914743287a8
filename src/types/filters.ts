import { AppType } from "@/core.constants";

export interface BooleanFilters {
  has2FA: boolean | null; // null = no filter, true = only with 2FA, false = only without 2FA
  hasPinSupport: boolean | null;
  hasPhoneRequired: boolean | null;
  hasBackup: boolean | null;
}

export interface AppFilters {
  category: AppType | "all";
  booleanFilters: BooleanFilters;
}

export const DEFAULT_BOOLEAN_FILTERS: BooleanFilters = {
  has2FA: null,
  hasPinSupport: null,
  hasPhoneRequired: null,
  hasBackup: null,
};

export const DEFAULT_APP_FILTERS: AppFilters = {
  category: "all",
  booleanFilters: DEFAULT_BOOLEAN_FILTERS,
};
