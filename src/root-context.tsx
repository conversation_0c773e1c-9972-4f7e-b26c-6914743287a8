"use client";

import { getAnalytics, isSupported } from "firebase/analytics";
import { initializeApp } from "firebase/app";
import type { User } from "firebase/auth";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getFunctions } from "firebase/functions";
import { getStorage } from "firebase/storage";
import { useRouter } from "next/navigation";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useState } from "react";

import { getUserRole } from "@/api/auth-api";

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const firestore = getFirestore(app);
export const firebaseStorage = getStorage(app);
export const firebaseAuth = getAuth();
export const firebaseFunctions = getFunctions(app);
export const analytics = isSupported().then((yes) =>
  yes ? getAnalytics(app) : null
);

interface RootContextProps {
  role: string;
  currentUser: User | null;
  setRole: (role: string) => void;
  resetUser: () => void;
  setCurrentUser: (user: User | null) => void;
}

export const RootContext = createContext<RootContextProps>(undefined as never);

export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [role, setRole] = useState("");
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
      if (user) {
        setCurrentUser(user);

        getUserRole().then((role) => {
          setRole(role);
          if (role !== "admin") {
            router.push("/");
          }
        });
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);

  const resetUser = () => {
    setRole("");
    setCurrentUser(null);
  };

  return (
    <RootContext.Provider
      value={{
        resetUser,
        currentUser,
        role,
        setCurrentUser,
        setRole,
      }}
    >
      {children}
    </RootContext.Provider>
  );
};

export const useRootContext = () => useContext(RootContext);
