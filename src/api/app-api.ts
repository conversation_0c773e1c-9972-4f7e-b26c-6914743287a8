import { App } from "@/core.constants";
import { firebaseStorage, firestore } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
} from "firebase/firestore";
import {
  deleteObject,
  getDownloadURL,
  ref,
  uploadBytes,
} from "firebase/storage";

const COLLECTION_NAME = "apps";
const STORAGE_FOLDER = "apps";

export const createApp = async (
  appData: Omit<App, "id" | "createdAt" | "updatedAt">
) => {
  try {
    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...appData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  } catch (error) {
    console.error("Error creating app:", error);
    throw error;
  }
};

export const updateApp = async (id: string, appData: Partial<App>) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...appData,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error updating app:", error);
    throw error;
  }
};

export const deleteApp = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting app:", error);
    throw error;
  }
};

export const getApps = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const apps: App[] = [];

    snapshot.forEach((doc) => {
      apps.push({ id: doc.id, ...doc.data() } as App);
    });

    return {
      apps,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching apps:", error);
    throw error;
  }
};

export const uploadInstructionImage = async (
  file: File,
  fileName: string
): Promise<string> => {
  try {
    const storageRef = ref(firebaseStorage, `${STORAGE_FOLDER}/${fileName}`);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
};

export const uploadAppLogo = async (
  file: File,
  fileName: string
): Promise<string> => {
  try {
    const storageRef = ref(
      firebaseStorage,
      `${STORAGE_FOLDER}/logos/${fileName}`
    );
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading app logo:", error);
    throw error;
  }
};

export const deleteInstructionImage = async (imageUrl: string) => {
  try {
    const imageRef = ref(firebaseStorage, imageUrl);
    await deleteObject(imageRef);
  } catch (error) {
    console.error("Error deleting image:", error);
    throw error;
  }
};

export const deleteAppLogo = async (imageUrl: string) => {
  try {
    const imageRef = ref(firebaseStorage, imageUrl);
    await deleteObject(imageRef);
  } catch (error) {
    console.error("Error deleting app logo:", error);
    throw error;
  }
};
