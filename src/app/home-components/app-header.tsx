"use client";

import React from "react";
import Image from "next/image";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import { Badge } from "@/components/ui/badge";

interface AppHeaderProps {
  app: App;
}

export const AppHeader = ({ app }: AppHeaderProps) => (
  <div className="flex items-start space-x-4 mb-6 pb-4 border-b border-neutral-200 dark:border-neutral-700">
    {app.appLogo ? (
      <Image
        src={app.appLogo}
        alt={`${app.name} logo`}
        width={60}
        height={60}
        className="w-15 h-15 object-contain rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800"
      />
    ) : (
      <div className="w-15 h-15 bg-gradient-to-br from-primary via-primary/90 to-primary/70 rounded-xl flex items-center justify-center">
        <span className="text-2xl font-bold text-white">
          {app.name.charAt(0)}
        </span>
      </div>
    )}
    <div className="flex-1 space-y-2">
      <h1 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
        {app.name}
      </h1>
      <Badge
        variant="secondary"
        className="text-sm px-3 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
      >
        {APP_TYPE_TEXT[app.type]}
      </Badge>
      <p className="text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed">
        Security evaluation and configuration for your {app.name} account.
      </p>
    </div>
  </div>
);
