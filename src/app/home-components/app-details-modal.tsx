"use client";

import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import { Shield } from "lucide-react";
import Image from "next/image";
import { EmergencyActionsSection } from "./shared/emergency-section";
import { PhoneNumberSection } from "./shared/phone-number-section";
import { PinSection } from "./shared/pin-section";
import { RecommendationsSection } from "./shared/recommendations-section";
import { SecurityFeaturesSection } from "./shared/security-features-section";
import { SecurityInstructionsSection } from "./shared/security-instructions-section";

interface AppDetailsModalProps {
  app: App | null;
  isOpen: boolean;
  onClose: () => void;
}

export const AppDetailsModal = ({
  app,
  isOpen,
  onClose,
}: AppDetailsModalProps) => {
  if (!app) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full sm:min-w-[700px] max-h-[95vh] overflow-y-auto bg-gradient-to-br from-neutral-900 via-neutral-900 to-neutral-800 border-white/10 shadow-2xl">
        <DialogHeader className="space-y-6 pb-8 border-b border-white/10">
          <div className="flex items-start space-x-6">
            {app.appLogo ? (
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <Image
                  src={app.appLogo}
                  alt={`${app.name} logo`}
                  width={80}
                  height={80}
                  className="relative w-20 h-20 object-contain rounded-2xl shadow-2xl border border-white/10 bg-white/5 backdrop-blur-sm"
                />
              </div>
            ) : (
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <div className="relative w-20 h-20 bg-gradient-to-br from-primary via-primary/90 to-primary/70 rounded-2xl flex items-center justify-center shadow-2xl border border-white/10">
                  <span className="text-3xl font-bold text-white">
                    {app.name.charAt(0)}
                  </span>
                </div>
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center shadow-lg border-2 border-neutral-900">
                  <Shield className="w-4 h-4 text-white" />
                </div>
              </div>
            )}
            <div className="flex-1 space-y-3">
              <DialogTitle className="text-4xl font-bold bg-gradient-to-r from-white via-white to-neutral-300 bg-clip-text text-transparent">
                {app.name}
              </DialogTitle>
              <Badge
                variant="secondary"
                className="text-sm px-4 py-2 bg-white/10 text-white border-white/20 backdrop-blur-sm hover:bg-white/20 transition-colors"
              >
                {APP_TYPE_TEXT[app.type]}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-12 pt-2">
          <SecurityFeaturesSection app={app} variant="modal" />
          <PhoneNumberSection app={app} variant="modal" />
          <PinSection app={app} variant="modal" />
          <EmergencyActionsSection app={app} variant="modal" />
          <SecurityInstructionsSection app={app} variant="modal" />
          <RecommendationsSection app={app} variant="modal" />
        </div>
      </DialogContent>
    </Dialog>
  );
};
