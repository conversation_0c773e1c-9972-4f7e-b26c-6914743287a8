"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { Smartphone } from "lucide-react";
import { CollapsibleSection } from "./collapsible-section";

interface PhoneNumberSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const PhoneNumberSection = ({
  app,
  variant = "drawer",
}: PhoneNumberSectionProps) => {
  if (!app.phoneNumber.description) return null;

  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={Smartphone}
      title="Phone Number"
      iconColorClass="bg-gradient-to-r from-green-500 to-emerald-500"
      variant={variant}
    >
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <Markdown
          content={app.phoneNumber.description}
          className={
            isModal
              ? "text-neutral-300 mb-4"
              : "text-neutral-700 dark:text-neutral-300 mb-4"
          }
        />

        {app.phoneNumber.link && (
          <Button
            asChild
            className={
              isModal
                ? "w-full max-w-[270px] bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                : "w-full justify-start bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 h-12  text-white"
            }
            variant={isModal ? "default" : "outline"}
          >
            {/* <Link
              href={app.phoneNumber.link}
              target="_blank"
              className="flex items-center justify-center"
            >
              <Smartphone className="w-4 h-4 mr-3" />
              <span className="flex-1">
                {app.phoneNumber.enabled
                  ? "Remove Phone Number"
                  : "Manage Phone Number"}
              </span>
              <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
            </Link> */}
          </Button>
        )}
      </div>
    </CollapsibleSection>
  );
};
