"use client";

import React from "react";
import { App } from "@/core.constants";
import { SecurityFeature } from "../security-feature";
import { CollapsibleSection } from "./collapsible-section";
import { Key, Download, Hash } from "lucide-react";

interface SecurityFeaturesSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

type SecurityFeatureConfig = {
  key: "yubikeys" | "pin" | "backup";
  icon: React.ElementType;
  title: string;
  description: string;
  color?: string;
  getColor?: (enabled: boolean) => string;
};

const securityFeatures: SecurityFeatureConfig[] = [
  {
    key: "yubikeys",
    icon: Key,
    title: "Yubikey Support",
    description: "Hardware security key authentication",
    color: "bg-blue-500",
  },
  {
    key: "pin",
    icon: Hash,
    title: "PIN Support",
    description: "PIN or Passkey authentication",
    color: "bg-green-500",
  },
  {
    key: "backup",
    icon: Download,
    title: "Backup or recovery codes",
    description: "Recovery codes for emergency access",
    color: "bg-gradient-to-r from-green-500 to-blue-500",
  },
];

const ModalSecurityFeature = ({
  feature,
  app,
}: {
  feature: SecurityFeatureConfig;
  app: App;
}) => {
  const featureData = app[feature.key];
  const enabled = featureData?.enabled ?? false;
  const color = feature.getColor
    ? feature.getColor(enabled)
    : feature.color ?? "bg-gray-500";

  const getFeatureTitle = () => {
    switch (feature.key) {
      case "yubikeys":
        return "Yubikey";
      case "pin":
        return "PIN";
      case "backup":
        return "Backup or recovery codes";
      default:
        return feature.title;
    }
  };

  const getStatusText = () => {
    return enabled ? "Available on the app" : "Not supported on the app";
  };

  const getStatusColor = () => {
    return enabled ? "text-green-400" : "text-red-400";
  };

  return (
    <div className="text-center p-3 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors">
      <div className="flex justify-center mb-2">
        <div className={`rounded-lg p-2 ${color}`}>
          <feature.icon className="w-4 h-4 text-white" />
        </div>
      </div>
      <div className="text-xs text-white font-medium">{getFeatureTitle()}</div>
      <div className={`text-xs mt-1 ${getStatusColor()}`}>
        {getStatusText()}
      </div>
    </div>
  );
};

export const SecurityFeaturesSection = ({
  app,
  variant = "drawer",
}: SecurityFeaturesSectionProps) => {
  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={Key}
      title="2FA and backup strategies"
      iconColorClass="bg-gradient-to-r from-blue-500 to-purple-500"
      variant={variant}
      defaultExpanded={true}
    >
      {isModal ? (
        <div className="grid grid-cols-2 gap-3">
          {securityFeatures
            .filter((feature) => {
              // Hide PIN feature if not enabled
              if (feature.key === "pin") {
                return app.pin?.enabled ?? false;
              }
              return true;
            })
            .map((feature) => (
              <ModalSecurityFeature
                key={feature.key}
                feature={feature}
                app={app}
              />
            ))}
        </div>
      ) : (
        <div className="space-y-3">
          {securityFeatures
            .filter((feature) => {
              // Hide PIN feature if not enabled
              if (feature.key === "pin") {
                return app.pin?.enabled ?? false;
              }
              return true;
            })
            .map((feature) => {
              const featureData = app[feature.key];
              const enabled = featureData?.enabled ?? false;
              const color = feature.getColor
                ? feature.getColor(enabled)
                : feature.color ?? "bg-gray-500";

              return (
                <SecurityFeature
                  key={feature.key}
                  icon={feature.icon}
                  title={feature.title}
                  enabled={enabled}
                  link={featureData?.link}
                  description={feature.description}
                  color={color}
                />
              );
            })}
        </div>
      )}
    </CollapsibleSection>
  );
};
