"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { App } from "@/core.constants";
import { AlertTriangle, ExternalLink } from "lucide-react";
import Link from "next/link";
import { CollapsibleSection } from "./collapsible-section";
import { Markdown } from "@/components/ui/markdown";

interface EmergencyActionsSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

interface EmergencyActionItemProps {
  emergencyLink: {
    link?: string;
    description?: string;
    label?: string;
  };
  variant?: "modal" | "drawer";
}

const EmergencyActionItem = ({
  emergencyLink,
  variant,
}: EmergencyActionItemProps) => {
  const isModal = variant === "modal";

  return (
    <div>
      {emergencyLink.link && (
        <Button
          asChild
          className={
            isModal
              ? "flex-1 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
              : "flex-1 justify-start h-12 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white border-0"
          }
          variant="default"
        >
          <Link
            href={emergencyLink.link}
            target="_blank"
            className="flex items-center justify-center"
          >
            <AlertTriangle className="w-4 h-4 mr-3" />
            <span className="flex-1">{emergencyLink.label}</span>
            <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
          </Link>
        </Button>
      )}
      {emergencyLink.description && (
        <Markdown
          content={emergencyLink.description}
          className={
            isModal
              ? "text-neutral-300 mb-4"
              : "text-neutral-700 dark:text-neutral-300 mb-4"
          }
        />
      )}
    </div>
  );
};

export const EmergencyActionsSection = ({
  app,
  variant = "drawer",
}: EmergencyActionsSectionProps) => {
  const validEmergencyLinks = app.emergency.links.filter(
    (link) => link.description && link.description.trim() !== ""
  );

  if (validEmergencyLinks.length === 0) return null;

  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={AlertTriangle}
      title={"Emergency plan"}
      iconColorClass="bg-gradient-to-r from-red-500 to-orange-500"
      variant={variant}
    >
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <div className="flex flex-row gap-4 flex-wrap">
          {validEmergencyLinks.map((emergencyLink, index) => (
            <EmergencyActionItem
              key={`emergency-${index}-${emergencyLink.label ?? index}`}
              emergencyLink={emergencyLink}
              variant={variant}
            />
          ))}
        </div>
      </div>
    </CollapsibleSection>
  );
};
