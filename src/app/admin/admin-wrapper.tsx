"use client";

import { useEffect } from "react";

import { useRootContext } from "@/root-context";
import { Role } from "@/core.constants";
import { AppManagement } from "./app-management";

export const AdminWrapper = () => {
  const { role } = useRootContext();

  useEffect(() => {
    document.documentElement.classList.add("dark");
  }, []);

  if (role !== Role.ADMIN) return null;

  return (
    <div className="mx-auto w-full max-w-6xl p-4">
      <AppManagement />
    </div>
  );
};
