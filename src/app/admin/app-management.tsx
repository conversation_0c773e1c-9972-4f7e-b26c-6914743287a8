"use client";

import { DocumentSnapshot } from "firebase/firestore";
import { Plus } from "lucide-react";
import { useCallback, useState } from "react";

import { deleteApp, getApps } from "@/api/app-api";
import { Button } from "@/components/ui/button";
import { AppsTable } from "@/components/apps-table";
import { App } from "@/core.constants";
import { usePaginationRequest } from "@/hooks/use-pagination-request";
import { ManageAppModal } from "./manage-app-modal";

export const AppManagement = () => {
  const [apps, setApps] = useState<App[]>([]);
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | undefined>();
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingApp, setEditingApp] = useState<App | undefined>();
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchApps = useCallback(async () => {
    try {
      const result = await getApps(20, lastDoc);
      setApps((prev) => [...prev, ...result.apps]);
      setLastDoc(result.lastDoc);
      setHasMoreItems(result.hasMore);
    } catch (error) {
      console.error("Error fetching apps:", error);
    }
  }, [lastDoc]);

  const initialFetch = useCallback(async () => {
    try {
      const result = await getApps(20);
      setApps(result.apps);
      setLastDoc(result.lastDoc);
      setHasMoreItems(result.hasMore);
    } catch (error) {
      console.error("Error fetching apps:", error);
    }
  }, []);

  const { loadingMarkup, reset } = usePaginationRequest({
    initialRequest: initialFetch,
    fetchRequest: fetchApps,
    hasMoreItems,
  });

  const handleCreateApp = () => {
    setEditingApp(undefined);
    setIsModalOpen(true);
  };

  const handleEditApp = (app: App) => {
    setEditingApp(app);
    setIsModalOpen(true);
  };

  const handleAppSaved = () => {
    setIsModalOpen(false);
    setEditingApp(undefined);
    setApps([]);
    setLastDoc(undefined);
    setHasMoreItems(true);
    reset();
  };

  const handleDeleteApp = async (app: App) => {
    setIsDeleting(true);
    try {
      await deleteApp(app.id);
      // Refresh the list
      setApps([]);
      setLastDoc(undefined);
      setHasMoreItems(true);
      reset();
    } catch (error) {
      console.error("Error deleting app:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">App Management</h1>
        <div className="flex gap-2">
          <Button onClick={handleCreateApp}>
            <Plus className="mr-2 h-4 w-4" />
            Add App
          </Button>
        </div>
      </div>

      <AppsTable
        apps={apps}
        showActions={true}
        onEditApp={handleEditApp}
        onDeleteApp={handleDeleteApp}
        isDeleting={isDeleting}
      />

      {loadingMarkup}

      <ManageAppModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        app={editingApp}
        onSave={handleAppSaved}
      />
    </div>
  );
};
